export const stdHeaders = {//important headers//std header do not have '_'
	"email": ["elv email", "emailaddress", "email address", "validemail", "bizemail", "ôªøemail", "ï»¿email", "to", "alternate_emails", "emails", "fixed_email", "customer email",],
	"domain": ["domains", "zb domain", "fixed_domain"],
	"first name": ["first", "zb first name", "firstname", "first_name", "customer first name", "shipping address first name", "billing address first name", "fix_first"],
	"middle name": ["middle_name", "middlename", "middle", "middle initial", "middle init"],
	"last name": ["last", "zb last name", "lastname", "last_name", "customer last name", "shipping address last name", "billing address last name", "fix_last"],
	"name": ["fullname", "contact name", "full_name", "unique_name", "fixed_fullname", "full name"],
	"address": ["address1", "address 1", "bizaddr", "mailing address", "address line 1", "address_line_1", "default street 1", "shipping address street 1", "billing address street 1", "fixed_address", "address 1'"],
	"address2": ["address 2", "address line 2", "address_line_2", "default street 2", "shipping address street 2", "billing address street 2", "fixed_address2"],
	"city": ["bizcity", "fixed_city", "default city", "shipping address city", "billing address city", "cities"],
	"state": ["st", "bizstate", "bizstate-dl", "state or province", "state_province_region", "fixed_state", "state / region", "default state/province", "province code", "shipping address state/province", "billing address state/province"],
	"zip5": ["zip", "zipcode", "bizzip", "zip1", "zip code", "postal code", "postal_code", "zip 1", "zip 5", "shipping address zip/postal code", "billing address zip", "fixed_zip5"],
	"zip4": ["zip2", "zip 2", "zip 4", "zip 2'"],
	"county": ["loccounty", "fixed_county"],
	"age": ["fixed_age"],
	"marriage": ["fixed_marriage", "marital status"],
	"income": ["household income", "hhi", "fixed_income"],
	"interest": ["interests", "fixed_interest"],
	"home value": ["current home value", "home", "homevalue", "fixed_home value"],
	"credit": ["wealth rating", "fixed_credit"],
	"net worth": ["networth", "fixed_net worth"],
	"title": ["bizcat", "title examiner", "occupation", "job title", "fixed_title"],
	"company": ["bizname", "company name", "shipping address company", "billing address company", "business name", "agency", "fixed_company",],
	"employees": ["size", "fixed_Employee"],
	"education": ["education level", "fixed_education"],
	"sic": ["sic4", "sic code", "fixed_sic"],
	"naics": ["naics6", "fixed_naics"],
	"phone": ["phone number", "company_phone", "phone1", "phone_number", "customer phone", "default phone", "fixed_phone", "telephone", "phone #", "customer phone", "shipping address phone", "billing address phone"],
	"gender": ["m/f", "gender code", "zb gender", "fixed_gender"],
	"department": ["fixed_department", "Dept"],
	"level": ["fixed_level"],
	"industry": ["fixed_industry"],
	"source": ["fixed_source"],
	"children": ["fixed_children"],

	"publicprivate": ["fixed_publicprivate"]
}
export const igHeads = [
	"",
	"ignore",
	"linkedin",
	"stiristaid",
	"sequence_datamart",
	"result",
	"originalorder",
	"region code",
	"validationstatus",
	"validationcode",
	"domain type",
	"role account",
	"duplicate",
	"zb status",
	"ôªøbizstate-dl",
	"status",
	"zb sub status",
	"zb account",
	"zb free email",
	"zb mx found",
	"zb mx record",
	"zb smtp provider",
	"zb did you mean",
	"zb disposable",
	"zb toxic domain",

	"to modlux",
	"bud1",
	"yes",
	"to modlux",
	"segmentname",
	"status", "channel", "date", "message", "bouncecategory", "bouncecategoryid",
	"msgid", "jobid", "statuschangedate", "textmessage", "messagesid", "subject", "datesent",
	"dateopened", "dateclicked", "ipaddress", "envelopefrom",
	"firm_id", "isln",//"nick_name","admitted","law_school","college","memberships","biographical","born","practice_areas","law_school_graduation_year",
	"website", "country",
	"linkedin", "cleansestatus",
	"whatsapp", "line", "facebook", "created_at", "updated_at", "contact_id",
	"fixed_invalid", "invalid",
	"acct#",
	"customer id", "customer_id", "external_customer_id", "accepts_marketing", "orders_count", "number_active_subscriptions", "number_subscriptions", "first_charge_processed_at", "card_error_in_dunning", "created_at", "hash",
	"tags", "accepts email marketing", "accepts sms marketing", "total spent", "total orders", "note", "tax exempt",
	"cleansestatus",
	"discount code", "number of retries", "number of products", "number of orders", "contains prepaid",
	"status", "next payment date", "next order date", "last order date", "created at", "canceled at", "paused at", "billing rules", "total value", "shop currency", "total value charged", "charged currency", "default country",
	"last activity taken", "last activity date", "active subscription count", "paused subscription count", "cancelled subscription count", "lifetime value (usd)",
	"shipping address country", "billing address country", "subscription id", "created date",
	"row_number",
	"postal address cass verified mailable"

]