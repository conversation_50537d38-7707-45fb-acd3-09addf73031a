const allAddressKeywords = new Set([
	"ST", "AVE", "<PERSON><PERSON><PERSON>", "RD", "DR", "LN", "PL", "CT", "NE", "NW", "SE", "SW", "E", "W", "N", "S", "WAY", "STE", "<PERSON>KW<PERSON>", "LBBY", "<PERSON>I<PERSON>", "PL<PERSON>", "CIR", "PARK", "HWY", "PKWY", "SHPG", "METRO", "FL", "CTR", "CSWY", "HALL", "INTERNATION", "HIGHWAY", "TASF", "THRUWAY", "SR", "BIRD", "ROUTE", "TER", "JR", "CENTER", "CV", "AVENUE", "RM", "TRL", "NORTE", "TESTS", "BOX", "ORCHRD", "RADS", "ROAD", "WHAR<PERSON>", "<PERSON>W<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>L<PERSON>", "<PERSON>LL<PERSON>", "VLG", "SQ", "HOUSE", "OFC", "SHIPYARD", "XING"
])

const startKeywords = new Set([
	"ANG-E212", "BLDG", "E6248", "FINANCE", "PO", "QUINCE", "RTE", "WJH", "WJH"
])

const address2Keywords = new Set(
	['#', 'APT', 'BLDG', 'BSMT', 'CASA', 'DEPT', 'FL', 'FRNT', 'HSE', 'LBBY', 'LOT', 'LOWR', 'NAPT', 'NO', 'NUM', 'OFC', 'PH', 'PMB', 'REAR', 'RM', 'SIDE', 'SLIP', 'SPC', 'STE', 'TRLR', 'UNIT', 'UPPR', "CUBE", "MS", "STOP"]
)